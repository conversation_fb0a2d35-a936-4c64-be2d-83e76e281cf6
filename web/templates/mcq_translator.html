{% extends "base.html" %}

{% block title %}MCQ Translator{% endblock %}

{% block content %}

<style>
/* Loader Styles */
.loader-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.loader {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loader-text {
    flex: 1;
    max-width: 400px;
}

#loaderTitle {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

#loaderSubtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 12px;
    font-weight: bold;
    color: #4CAF50;
    min-width: 35px;
}

.translation-results {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.metadata {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.translation-text {
    white-space: pre-wrap;
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    max-height: 500px;
    overflow-y: auto;
}

.buttons-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.view-button, .download-button {
    background-color: #4CAF50;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.view-button:hover, .download-button:hover {
    background-color: #45a049;
}

.view-button:disabled, .download-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    opacity: 0.6;
}

.view-button:disabled:hover, .download-button:disabled:hover {
    background-color: #cccccc;
}

.download-button {
    background-color: #2196F3;
}

.download-button:hover {
    background-color: #1976D2;
}

.link-container {
    margin-top: 15px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.link-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.link-input {
    width: calc(100% - 100px);
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
    font-family: monospace;
    font-size: 14px;
}

.copy-button {
    background-color: #FF9800;
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.copy-button:hover {
    background-color: #F57C00;
}
select[name='sourceLanguage'],
    select[name='destinationLanguage']{
        padding: 0.6rem !important;
        background: #f4f4f4;
        border-radius: 6px !important;
        border: 1px solid #ccc !important;
        font-size: 1rem !important;
        margin-bottom: 1.2rem !important;
    }
</style>
<div class="test-container">
    <div class="test-card">
        <h1>MCQ Translator</h1>
        <p class="test-description">Translate MCQs from one language to another</p>

        <form id="translatorForm" class="login-form" enctype="multipart/form-data">
            <label for="pdfFile">Upload PDF File</label>
            <input type="file" id="pdfFile" name="pdfFile" accept=".pdf" required>

            <label for="totalQuestions">Total Questions</label>
            <input type="number" id="totalQuestions" name="totalQuestions" min="1" value="10" required>

            <label for="sourceLanguage">Source Language</label>
            <select id="sourceLanguage" name="sourceLanguage" required>
                <option value="English">English</option>
                <option value="Hindi">Hindi</option>
                <option value="Tamil">Tamil</option>
                <option value="Assamese">Assamese</option>
                <option value="Telugu">Telugu</option>
                <option value="Kannada">Kannada</option>
                <option value="Malayalam">Malayalam</option>
                <option value="Marathi">Marathi</option>
                <option value="Bengali">Bengali</option>
                <option value="Gujarati">Gujarati</option>
                <option value="Urdu">Urdu</option>
            </select>

            <label for="destinationLanguage">Destination Language</label>
            <select id="destinationLanguage" name="destinationLanguage" required>
                <option value="Hindi">Hindi</option>
                <option value="English">English</option>
                <option value="Tamil">Tamil</option>
                <option value="Assamese">Assamese</option>
                <option value="Telugu">Telugu</option>
                <option value="Kannada">Kannada</option>
                <option value="Malayalam">Malayalam</option>
                <option value="Marathi">Marathi</option>
                <option value="Bengali">Bengali</option>
                <option value="Gujarati">Gujarati</option>
                <option value="Urdu">Urdu</option>
            </select>

            <button type="submit">Translate MCQ</button>
        </form>
        <div id="loaderContainer" style="display: none; margin-top: 20px;">
            <div class="loader-wrapper">
                <div class="loader"></div>
                <div class="loader-text">
                    <div id="loaderTitle">Processing Translation...</div>
                    <div id="loaderSubtitle">Please wait while we process your PDF file</div>
                    <div class="progress-bar-container" style="display: none;">
                        <div class="progress-bar">
                            <div id="progressBarFill" class="progress-bar-fill"></div>
                        </div>
                        <div id="progressText" class="progress-text">0%</div>
                    </div>
                </div>
            </div>
        </div>

        <div id="logContainer" style="display: none; margin-top: 20px;">
            <h3>Progress Log: <span id="timer" style="color: #007bff; font-weight: bold;">00:00</span></h3>
            <div id="progressLog" class="progress-log"></div>
        </div>

        <div id="resultContainer" style="display: none; margin-top: 20px;">
            <h3>Translation Results:</h3>
            <div id="translationResults" class="translation-results"></div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('translatorForm');
    const loaderContainer = document.getElementById('loaderContainer');
    const loaderTitle = document.getElementById('loaderTitle');
    const loaderSubtitle = document.getElementById('loaderSubtitle');
    const progressBarFill = document.getElementById('progressBarFill');
    const progressText = document.getElementById('progressText');
    const logContainer = document.getElementById('logContainer');
    const progressLog = document.getElementById('progressLog');
    const resultContainer = document.getElementById('resultContainer');
    const translationResults = document.getElementById('translationResults');

    function addLogEntry(message) {
        const entry = document.createElement('div');
        entry.className = 'log-entry';
        entry.textContent = message;
        progressLog.appendChild(entry);
        progressLog.scrollTop = progressLog.scrollHeight;
    }

    function updateLoader(title, subtitle, progress) {
        loaderTitle.textContent = title;
        loaderSubtitle.textContent = subtitle;
        progressBarFill.style.width = progress + '%';
        progressText.textContent = progress + '%';
    }

    function showLoader() {
        loaderContainer.style.display = 'block';
        updateLoader('Initializing Translation...', 'Preparing to process your PDF file', 0);
    }

    function hideLoader() {
        loaderContainer.style.display = 'none';
    }

    // Timer functionality
    let startTime = null;
    let timerInterval = null;

    function startTimer() {
        startTime = new Date();
        timerInterval = setInterval(updateTimer, 1000);
    }

    function updateTimer() {
        if (startTime) {
            const now = new Date();
            const elapsed = Math.floor((now - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            const timerElement = document.getElementById('timer');
            if (timerElement) {
                timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }
    }

    function stopTimer() {
        if (timerInterval) {
            clearInterval(timerInterval);
            timerInterval = null;
        }
        if (startTime) {
            const now = new Date();
            const elapsed = Math.floor((now - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        return '00:00';
    }

    function resetTimer() {
        stopTimer();
        startTime = null;
        const timerElement = document.getElementById('timer');
        if (timerElement) {
            timerElement.textContent = '00:00';
        }
    }

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Clear previous results
        progressLog.innerHTML = '';
        translationResults.innerHTML = '';
        resultContainer.style.display = 'none';

        // Reset timer and show loader
        resetTimer();
        showLoader();
        logContainer.style.display = 'block';

        // Start the timer
        startTimer();

        // Get form values
        const pdfFile = document.getElementById('pdfFile').files[0];
        const totalQuestions = document.getElementById('totalQuestions').value;
        const sourceLanguage = document.getElementById('sourceLanguage').value;
        const destinationLanguage = document.getElementById('destinationLanguage').value;

        // Validate inputs
        if (!pdfFile || !totalQuestions || !sourceLanguage || !destinationLanguage) {
            addLogEntry('Error: All fields are required');
            hideLoader();
            return;
        }

        if (sourceLanguage === destinationLanguage) {
            addLogEntry('Error: Source and destination languages must be different');
            hideLoader();
            return;
        }

        addLogEntry(`Starting translation for file: ${pdfFile.name}`);
        addLogEntry(`Source language: ${sourceLanguage}, Destination language: ${destinationLanguage}`);

        try {
            // Create FormData for file upload
            const formData = new FormData();
            formData.append('pdfFile', pdfFile);
            formData.append('total_questions', totalQuestions);
            formData.append('source_language', sourceLanguage);
            formData.append('destination_language', destinationLanguage);
            formData.append('username', 'web_user');

            // Call the API to start translation
            const response = await fetch('/api/mcq-translator-file', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.status === 'started') {
                // Task started successfully, begin polling
                addLogEntry(`Task started with ID: ${data.task_id}`);
                addLogEntry('Polling for status updates...');

                // Start polling for status
                pollTaskStatus(data.task_id);
            } else {
                // Stop timer and get final time
                const finalTime = stopTimer();

                // Hide loader
                hideLoader();

                addLogEntry(`Error: ${data.message} (Failed after ${finalTime})`);
            }

        } catch (error) {
            // Stop timer and get final time
            const finalTime = stopTimer();

            // Hide loader
            hideLoader();

            addLogEntry(`Error: ${error.message} (Failed after ${finalTime})`);
        }
    });

    // Polling function for task status
    async function pollTaskStatus(taskId) {
        const pollInterval = 5000; // 5 seconds
        let pollCount = 0;
        const maxPolls = 360; // Maximum 30 minutes (360 * 5 seconds)

        const poll = async () => {
            try {
                pollCount++;
                addLogEntry(`Checking status...`);

                const response = await fetch('/api/mcq-translator-file/status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        task_id: taskId
                    })
                });

                const statusResult = await response.json();

                if (statusResult.status === 'COMPLETED') {
                    // Task completed successfully
                    const finalTime = stopTimer();
                    hideLoader();

                    addLogEntry(`Translation completed successfully! (Completed in ${finalTime})`);

                    // Display results from the task result
                    if (statusResult.result) {
                        displayTranslationResults(statusResult.result, finalTime);
                    } else {
                        addLogEntry('Task completed but no result data available');
                    }

                    return; // Stop polling

                } else if (statusResult.status === 'FAILED') {
                    // Task failed
                    const finalTime = stopTimer();
                    hideLoader();

                    addLogEntry(`Translation failed: ${statusResult.message} (Failed after ${finalTime})`);
                    return; // Stop polling

                } else if (statusResult.status === 'not_found') {
                    // Task not found
                    const finalTime = stopTimer();
                    hideLoader();

                    addLogEntry(`Task not found: ${statusResult.message} (Failed after ${finalTime})`);
                    return; // Stop polling

                } else {
                    // Task still in progress
                    addLogEntry(`Status: ${statusResult.status} - ${statusResult.message || 'Processing...'}`);

                    // Continue polling if we haven't exceeded max polls
                    if (pollCount < maxPolls) {
                        setTimeout(poll, pollInterval);
                    } else {
                        // Timeout reached
                        const finalTime = stopTimer();
                        hideLoader();
                        addLogEntry(`Translation timed out after ${maxPolls * pollInterval / 1000 / 60} minutes (Timed out after ${finalTime})`);
                    }
                }

            } catch (error) {
                addLogEntry(`Error checking status: ${error.message}`);

                // Continue polling on error (might be temporary network issue)
                if (pollCount < maxPolls) {
                    setTimeout(poll, pollInterval);
                } else {
                    const finalTime = stopTimer();
                    hideLoader();
                    addLogEntry(`Polling failed after ${maxPolls} attempts (Failed after ${finalTime})`);
                }
            }
        };

        // Start polling
        setTimeout(poll, pollInterval);
    }

    // Function to display translation results
    function displayTranslationResults(result, completionTime) {
        // Show the translation results container
        resultContainer.style.display = 'block';

        // Create buttons container
        const buttonsContainer = document.createElement('div');
        buttonsContainer.className = 'buttons-container';

        // Create a button to view the translated content
        const viewButton = document.createElement('button');
        viewButton.textContent = 'View Translated Content';
        viewButton.className = 'view-button';
        viewButton.addEventListener('click', async function() {
            try {
                // Show loading state on button
                const originalText = viewButton.textContent;
                viewButton.textContent = 'Loading...';
                viewButton.disabled = true;

                addLogEntry('Fetching translated content...');

                const contentResponse = await fetch(`/api/get-translated-mcq-content/${result.translation_id}`);
                const contentData = await contentResponse.json();

                if (contentData.status === 'success') {
                    // Clear previous content
                    translationResults.innerHTML = '';
                    translationResults.appendChild(buttonsContainer);

                    // Add the translated text in a pre element
                    const textPre = document.createElement('pre');
                    textPre.className = 'translation-text';
                    textPre.textContent = contentData.content;
                    translationResults.appendChild(textPre);

                    addLogEntry('Translated content loaded successfully');
                } else {
                    addLogEntry(`Error fetching translated content: ${contentData.message}`);
                }
            } catch (error) {
                addLogEntry(`Error: ${error.message}`);
            } finally {
                // Restore button state
                viewButton.textContent = originalText;
                viewButton.disabled = false;
            }
        });

        // Create download button
        const downloadButton = document.createElement('button');
        downloadButton.textContent = 'Download Translated File';
        downloadButton.className = 'download-button';
        downloadButton.addEventListener('click', function() {
            // Show loading state on button
            const originalText = downloadButton.textContent;
            downloadButton.textContent = 'Downloading...';
            downloadButton.disabled = true;

            const downloadUrl = `/api/download-translated-mcq/${result.translation_id}`;
            window.open(downloadUrl, '_blank');
            addLogEntry('Download started...');

            // Restore button state after a short delay
            setTimeout(() => {
                downloadButton.textContent = originalText;
                downloadButton.disabled = false;
            }, 2000);
        });

        // Create link display and copy functionality
        const linkContainer = document.createElement('div');
        linkContainer.className = 'link-container';

        const linkLabel = document.createElement('label');
        linkLabel.textContent = 'Download Link:';

        const linkInput = document.createElement('input');
        linkInput.type = 'text';
        linkInput.className = 'link-input';
        linkInput.value = `${window.location.origin}/api/download-translated-mcq/${result.translation_id}`;
        linkInput.readOnly = true;

        const copyButton = document.createElement('button');
        copyButton.textContent = 'Copy Link';
        copyButton.className = 'copy-button';
        copyButton.addEventListener('click', function() {
            linkInput.select();
            document.execCommand('copy');
            copyButton.textContent = 'Copied!';
            setTimeout(() => {
                copyButton.textContent = 'Copy Link';
            }, 2000);
        });

        // Add elements to containers
        buttonsContainer.appendChild(viewButton);
        buttonsContainer.appendChild(downloadButton);

        linkContainer.appendChild(linkLabel);
        linkContainer.appendChild(linkInput);
        linkContainer.appendChild(copyButton);

        translationResults.appendChild(buttonsContainer);
        translationResults.appendChild(linkContainer);

        // Add completion summary
        addLogEntry(`Translation completed in ${completionTime}`);
        addLogEntry(`Translation ID: ${result.translation_id}`);
        addLogEntry(`File: ${result.filename}`);
        addLogEntry(`Questions: ${result.total_questions}`);
        addLogEntry(`${result.source_language} → ${result.destination_language}`);
    }
});
</script>

{% endblock %}